[2025-06-17 09:51:44] local.ERROR: There are no commands defined in the "log" namespace. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"log\" namespace. at C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('log')
#1 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('log:clear')
#2 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\swudb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\swudb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\swudb\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-09-03 14:22:04] local.ERROR: There are no commands defined in the "migration" namespace.

Did you mean this?
    migrate {"exception":"[object] (Symfony\\Component\\Console\\Exception\\NamespaceNotFoundException(code: 0): There are no commands defined in the \"migration\" namespace.

Did you mean this?
    migrate at C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php:660)
[stacktrace]
#0 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(709): Symfony\\Component\\Console\\Application->findNamespace('migration')
#1 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(284): Symfony\\Component\\Console\\Application->find('migration:refre...')
#2 C:\\xampp\\htdocs\\swudb\\vendor\\symfony\\console\\Application.php(193): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 C:\\xampp\\htdocs\\swudb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Console\\Kernel.php(198): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 C:\\xampp\\htdocs\\swudb\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(1235): Illuminate\\Foundation\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#5 C:\\xampp\\htdocs\\swudb\\artisan(16): Illuminate\\Foundation\\Application->handleCommand(Object(Symfony\\Component\\Console\\Input\\ArgvInput))
#6 {main}
"} 
[2025-09-03 22:00:16] local.INFO: BOT: === AVVIO SESSIONE BOT TELEGRAM ===  
[2025-09-03 22:00:16] local.INFO: BOT: Timestamp: 2025-09-03 22:00:16  
[2025-09-03 22:00:16] local.INFO: BOT: Token configurato: SI  
[2025-09-03 22:00:16] local.INFO: BOT: Webhook URL: https://www.unlimiteddb.net  
[2025-09-03 22:00:16] local.INFO: BOT: =====================================  
[2025-09-03 22:00:16] local.INFO: BOT: === AVVIO SESSIONE BOT TELEGRAM ===  
[2025-09-03 22:00:16] local.INFO: BOT: Timestamp: 2025-09-03 22:00:16  
[2025-09-03 22:00:16] local.INFO: BOT: Token configurato: SI  
[2025-09-03 22:00:16] local.INFO: BOT: Webhook URL: https://www.unlimiteddb.net  
[2025-09-03 22:00:16] local.INFO: BOT: =====================================  
[2025-09-10 17:31:31] local.INFO: Starting card import process with API integration  
[2025-09-10 17:31:31] local.INFO: Using external thread ID: scan_68c199d27d539_1757518290  
[2025-09-10 17:31:31] local.INFO: Starting card import process with API integration  
[2025-09-10 17:31:35] local.ERROR: Failed to send Telegram alert: cURL error 60: SSL certificate problem: self-signed certificate in certificate chain (see https://curl.haxx.se/libcurl/c/libcurl-errors.html) for https://api.telegram.org/bot7717265706:AAH5chf4Ae3vsFSt7158K-RFWdh9BudnnQc/sendMessage  
